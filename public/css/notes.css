/**
 * 便签组件样式表 - notes.css
 *
 * 本文件定义了便签组件的所有样式，包括：
 * 1. 便签容器：大小、阴影、边框和背景色
 * 2. 便签头部：标题、关闭按钮和拖动控制
 * 3. 内容区域：文本编辑区样式和交互行为
 * 4. 交互元素：自定义滚动条、调整大小控件
 * 5. 便签主题：不同颜色主题的便签样式
 *
 * 这些样式使便签具有可拖拽、可调整大小、可编辑的特性，
 * 并通过不同的颜色主题提供视觉区分，增强用户体验。
 */

/* 便签相关样式 */

/* ===== 便签样式 ===== */
.note {
  position: absolute;
  width: 200px;
  height: 200px;
  border-radius: 8px; /* 增大圆角 */
  /* 优化阴影效果，增强边界感 */
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05), 0 2px 8px rgba(0, 0, 0, 0.09),
    0 4px 10px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* transition: box-shadow 0.2s; */
  /* 添加缩放相关样式 */
  resize: none; /* 移除浏览器原生缩放控件，使用自定义控件 */
  min-width: 150px;
  min-height: 150px;
  /* 移除最大尺寸限制 */
  /* backface-visibility: hidden; */
  /* transform: translateZ(0); */ /* 尝试提升到单独的渲染层以改善缩放时的文本模糊 */
}

.note:hover {
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.07), 0 4px 12px rgba(0, 0, 0, 0.12),
    0 8px 16px rgba(0, 0, 0, 0.05);
}

/* 标题容器 - 包含标题和关闭按钮 */
.note-title-container {
  display: flex;
  align-items: center;
  padding: 12px; /* 统一上下左右内边距 */
  background-color: transparent;
  z-index: 2;
  cursor: move; /* 标题容器也可拖动 */
  user-select: none;
  border-top-left-radius: 8px; /* 上部圆角 */
  border-top-right-radius: 8px; /* 上部圆角 */
  min-height: 36px; /* 确保最小高度容纳元素 */
  overflow: hidden; /* 防止内容溢出 */
  position: relative; /* 为绝对定位的关闭按钮提供参考 */
}

/* 启用便签标题 */
.note-title {
  display: inline-block; /* 使宽度自适应内容 */
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  font-size: 13px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.7); /* 提高对比度 */
  max-width: calc(100% - 40px); /* 留出关闭按钮的空间和间距 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background-color: rgba(0, 0, 0, 0.04); /* 添加灰色底色 */
  padding: 4px 8px; /* 增加内边距 */
  border-radius: 4px; /* 圆角 */
  /* 字体渲染优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 可编辑状态的标题 */
.note-title.editing {
  cursor: text;
  background-color: rgba(255, 255, 255, 0.7); /* 编辑时背景更亮 */
  border-radius: 4px;
  padding: 4px 8px; /* 保持与非编辑状态相同的内边距 */
  min-width: 60px;
  display: inline-block; /* 与非编辑状态保持一致 */
  white-space: nowrap;
  outline: none;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05); /* 轻微的边框效果 */
  max-width: calc(100% - 40px); /* 与非编辑状态保持一致 */
}

/* 关闭按钮 - 调整样式 */
.note-close {
  width: 22px;
  height: 22px;
  min-width: 22px; /* 确保最小宽度 */
  flex-shrink: 0; /* 防止按钮被压缩 */
  margin-left: 8px; /* 与标题保持间距 */
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.04); /* 与标题底色一致 */
  color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
  position: absolute; /* 使用绝对定位 */
  right: 12px; /* 距离右侧12px，与容器padding对齐 */
  top: 50%; /* 垂直居中 */
  transform: translateY(-50%); /* 精确垂直居中 */
}

.note-close:hover {
  background-color: rgba(0, 0, 0, 0.08); /* 悬停时稍微深一点 */
  color: #e74c3c;
  transform: translateY(-50%); /* 保持垂直居中，移除scale */
}

/* 关闭按钮禁用状态 */
.note-close.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none; /* 禁止所有鼠标事件 */
}

.note-close.disabled:hover {
  background-color: rgba(0, 0, 0, 0.04); /* 保持原始背景色 */
  color: rgba(0, 0, 0, 0.5); /* 保持原始颜色 */
}

/* 便签内容区域容器 */
.note-body {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 8px; /* 与便签整体圆角一致 */
}

/* 文本区域 */
.note-content {
  width: 100%;
  height: 100%;
  padding: 12px 14px; /* 增加内边距 */
  border: none;
  outline: none;
  resize: none;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6; /* 增加行高提高可读性 */
  color: rgba(0, 0, 0, 0.87); /* 提高对比度，从0.8调整到0.87 */
  background-color: transparent;
  overflow-y: auto;
  box-sizing: border-box; /* 确保padding不会增加元素总尺寸 */
  /* 字体渲染优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "liga" 1, "kern" 1;
  /* 隐藏原生滚动条 */
  scrollbar-width: none; /* Firefox */
}

/* 隐藏Webkit浏览器的原生滚动条 */
.note-content::-webkit-scrollbar {
  display: none;
}

/* 自定义滚动条容器 */
.custom-scrollbar {
  position: absolute;
  top: 36px; /* Adjust to start below the title area */
  right: 0;
  width: 6px;
  height: calc(100% - 36px); /* Adjust height for content area only */
  opacity: 0;
  /* transition: opacity 0.2s ease; */
  z-index: 2;
  pointer-events: none;
}

/* 便签悬停或文本区域聚焦时显示滚动条 */
.note:hover .custom-scrollbar,
.note-content:focus ~ .custom-scrollbar {
  opacity: 1;
}

/* 滚动条滑块 */
.scrollbar-thumb {
  position: absolute;
  right: 1px;
  width: 4px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  transition: background-color 0.2s ease;
  display: none; /* 默认隐藏滚动条滑块 */
}

.scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

/* 调整大小控件 - 优化外观 */
.note-resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  background-color: transparent; /* 移除灰色背景 */
  cursor: nwse-resize;
  border-radius: 0 0 8px 0; /* 与便签圆角匹配 */
  /* transition: opacity 0.2s; */
  z-index: 3;
  /* 添加调整大小图标 */
  background-image: none; /* 移除图标 */
  background-position: bottom right;
  background-repeat: no-repeat;
  opacity: 0; /* 默认完全透明 */
}

.note-resize-handle:hover {
  background-color: transparent; /* 悬停时也保持透明 */
  opacity: 0.7; /* 悬停时增加不透明度 */
}

/* 便签颜色 - 更浅淡的色彩 */
.note-yellow {
  background-color: #fffad6;
}
.note-blue {
  background-color: #e1f3fd;
}
.note-green {
  background-color: #e5f9f0;
}
.note-pink {
  background-color: #fef1f0;
}
.note-purple {
  background-color: #f2e8f7;
}

/* Markdown预览区域样式已移至markdown.css，避免重复定义 */
/* 这里只保留便签特定的预览样式覆盖 */
.note .markdown-preview {
  word-break: break-word; /* 便签中的文本换行处理 */
}

/* 隐藏Markdown预览区域的滚动条 */
.markdown-preview::-webkit-scrollbar {
  display: none;
}

/* 底部留白容器 */
.note-bottom-spacer {
  width: 100%;
  height: 30px;
  flex-shrink: 0; /* 防止被压缩 */
}
