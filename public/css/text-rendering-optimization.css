/**
 * 文本渲染优化样式表 - text-rendering-optimization.css
 *
 * 本文件专门处理文本渲染清晰度优化，确保在不同分辨率、设备和浏览器上
 * 文本都能达到最佳的清晰度和可读性。包含以下优化：
 *
 * 1. 现代字体技术支持（光学尺寸、可变字体等）
 * 2. 精细化DPI适配策略
 * 3. 跨浏览器文本渲染一致性
 * 4. Canvas缩放环境下的文本优化
 * 5. 性能优化的文本渲染
 * 6. 设备特定的渲染策略
 */

/* ========================================
   现代字体栈定义
   ======================================== */

:root {
  /* 现代化的系统字体栈，优先使用可变字体 */
  --font-family-system:
    /* 系统UI字体（最新） */ system-ui,
    /* 苹果系统字体 */ -apple-system, BlinkMacSystemFont,
    /* Windows 11可变字体 */ "Segoe UI Variable", "Segoe UI",
    /* Android/Chrome OS */ Roboto, /* 跨平台备选 */ "Helvetica Neue", Arial,
    /* 最终备选 */ sans-serif;

  /* 等宽字体栈（用于代码） */
  --font-family-mono:
    /* 现代等宽字体 */ "SF Mono", "Monaco", "Cascadia Code",
    /* 开源等宽字体 */ "Fira Code", "JetBrains Mono",
    /* 传统等宽字体 */ "Source Code Pro", Consolas, /* 系统备选 */ "Courier New",
    monospace;
}

/* ========================================
   基础文本渲染优化
   ======================================== */

/* 全局基础设置 - 使用现代字体技术 */
*,
*::before,
*::after {
  /* 现代字体特性 */
  font-optical-sizing: auto; /* 自动光学尺寸调整 */
  font-variant-ligatures: contextual; /* 智能连字 */
  font-kerning: auto; /* 自动字距调整 */

  /* 文本渲染基础设置 */
  text-rendering: optimizeLegibility;

  /* 防止移动端字体缩放 */
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* 主要文本元素的字体族设置 */
body,
input,
textarea,
select,
button {
  font-family: var(--font-family-system);
}

/* 代码元素使用等宽字体 */
code,
pre,
kbd,
samp,
.code {
  font-family: var(--font-family-mono);
}

/* ========================================
   设备和分辨率特定优化
   ======================================== */

/* 超高DPI屏幕 (4K/5K显示器, 300dpi+) */
@media (min-resolution: 300dpi) {
  * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeQuality;
  }

  /* 超高分辨率下可以使用更细的字体 */
  .note-content,
  .markdown-preview {
    font-weight: 300;
    letter-spacing: -0.01em;
  }
}

/* 标准高DPI屏幕 (Retina, 192-299dpi) */
@media (min-resolution: 192dpi) and (max-resolution: 299dpi) {
  * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeQuality;
  }

  .note-content,
  .markdown-preview {
    font-weight: 400;
    letter-spacing: 0;
  }

  /* 小字体在高DPI下的特殊处理 */
  .small-text,
  .note-title {
    font-weight: 500;
    letter-spacing: 0.005em;
  }
}

/* 标准DPI屏幕 (96-191dpi) - 大多数桌面显示器 */
@media (min-resolution: 96dpi) and (max-resolution: 191dpi) {
  * {
    /* 使用子像素渲染获得最佳清晰度 */
    -webkit-font-smoothing: subpixel-antialiased;
    -moz-osx-font-smoothing: auto;
    text-rendering: optimizeLegibility;
  }

  .note-content,
  .markdown-preview {
    font-weight: 400;
    letter-spacing: 0.01em; /* 轻微增加字间距提高可读性 */
  }

  /* 小字体需要更粗一些以保持清晰 */
  .small-text,
  .note-title {
    font-weight: 600;
    letter-spacing: 0.02em;
  }
}

/* 低DPI屏幕 (72-95dpi) - 老旧显示器 */
@media (max-resolution: 95dpi) {
  * {
    /* 低DPI下使用浏览器默认渲染 */
    -webkit-font-smoothing: auto;
    -moz-osx-font-smoothing: auto;
    text-rendering: optimizeSpeed;
  }

  .note-content,
  .markdown-preview {
    font-weight: 500; /* 增加字体粗细以提高可读性 */
    letter-spacing: 0.02em;
    /* 添加轻微的文本阴影增强对比度 */
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
  }

  .small-text,
  .note-title {
    font-weight: 700;
    letter-spacing: 0.03em;
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.15);
  }
}

/* ========================================
   浏览器特定优化
   ======================================== */

/* Firefox特定优化 */
@-moz-document url-prefix() {
  .note-content,
  .markdown-preview {
    /* Firefox在某些情况下需要特殊处理 */
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Safari特定优化 */
@supports (-webkit-appearance: none) {
  .note-content,
  .markdown-preview {
    /* Safari的字体渲染优化 */
    -webkit-font-smoothing: antialiased;
  }
}

/* Chrome/Edge特定优化 */
@supports (font-variation-settings: normal) {
  .note-content,
  .markdown-preview {
    /* 支持可变字体的浏览器 */
    font-variation-settings: "wght" 400;
  }
}

/* ========================================
   便签特定的文本优化
   ======================================== */

/* 便签内容区域的特殊优化 */
.note-content {
  /* 确保使用优化的字体族 */
  font-family: var(--font-family-system);

  /* 行高优化，提高可读性 */
  line-height: 1.6;

  /* 字体特性优化 */
  font-feature-settings: "liga" 1, "kern" 1, "calt" 1;

  /* 文本选择优化 */
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
}

/* Markdown预览区域的优化 */
.markdown-preview {
  /* 继承便签内容的优化设置 */
  font-family: var(--font-family-system);
  line-height: 1.6;
  font-feature-settings: "liga" 1, "kern" 1, "calt" 1;

  /* 预览模式下的特殊优化 */
  text-align: left;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 代码块的特殊优化 */
.markdown-preview code,
.markdown-preview pre {
  font-family: var(--font-family-mono);
  /* 代码字体不使用连字 */
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0, "calt" 0;
}

/* ========================================
   Canvas缩放环境优化
   ======================================== */

/* 当便签容器被缩放时的文本优化 */
.note-container[style*="scale"] .note-content,
.note-container[style*="scale"] .markdown-preview {
  /* 缩放时使用更快的渲染模式 */
  text-rendering: optimizeSpeed;

  /* 缩放时禁用某些字体特性以提高性能 */
  font-feature-settings: "kern" 0, "liga" 0;

  /* 使用硬件加速 */
  will-change: transform;
  transform: translateZ(0);
}

/* 小缩放比例时的特殊处理 */
.note-container[style*="scale(0."] .note-content,
.note-container[style*="scale(0."] .markdown-preview {
  /* 小缩放时增加字体粗细 */
  font-weight: 600;
  letter-spacing: 0.01em;
}

/* 大缩放比例时的特殊处理 */
.note-container[style*="scale(1.5)"] .note-content,
.note-container[style*="scale(1.5)"] .markdown-preview,
.note-container[style*="scale(2)"] .note-content,
.note-container[style*="scale(2)"] .markdown-preview {
  /* 大缩放时可以使用更细的字体 */
  font-weight: 300;
  letter-spacing: -0.005em;
}

/* ========================================
   移动设备和触摸屏优化
   ======================================== */

/* 移动设备的文本渲染优化 */
@media (hover: none) and (pointer: coarse) {
  .note-content,
  .markdown-preview {
    /* 移动设备上使用更粗的字体以提高可读性 */
    font-weight: 500;
    letter-spacing: 0.01em;

    /* 移动设备上的字体平滑 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    /* 优化触摸选择 */
    -webkit-touch-callout: default;
    -webkit-user-select: text;
  }

  /* 移动设备上的小字体特殊处理 */
  .small-text,
  .note-title {
    font-weight: 600;
    letter-spacing: 0.02em;
    /* 确保最小字体大小 */
    font-size: max(12px, 1em);
  }
}

/* 平板设备的特殊优化 */
@media (min-width: 768px) and (hover: none) {
  .note-content,
  .markdown-preview {
    /* 平板上可以使用稍细的字体 */
    font-weight: 400;
    letter-spacing: 0.005em;
  }
}

/* ========================================
   性能优化和动画期间的处理
   ======================================== */

/* 动画期间的文本渲染优化 */
.animating,
.note.dragging,
.note.resizing {
  /* 动画期间使用快速渲染模式 */
  text-rendering: optimizeSpeed;

  /* 禁用字体特性以提高性能 */
  font-feature-settings: normal;
  font-variant-ligatures: none;

  /* 使用硬件加速 */
  will-change: transform;
  transform: translateZ(0);
}

/* 动画结束后恢复优化设置 */
.note:not(.animating):not(.dragging):not(.resizing) .note-content,
.note:not(.animating):not(.dragging):not(.resizing) .markdown-preview {
  /* 恢复高质量渲染 */
  text-rendering: optimizeLegibility;
  font-feature-settings: "liga" 1, "kern" 1, "calt" 1;
  font-variant-ligatures: contextual;

  /* 移除硬件加速 */
  will-change: auto;
  transform: none;
}

/* ========================================
   主题和对比度优化
   ======================================== */

/* 暗色主题下的文本优化 */
@media (prefers-color-scheme: dark) {
  .note-content,
  .markdown-preview {
    /* 暗色背景下需要更强的字体平滑 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    /* 暗色主题下稍微增加字体重量 */
    font-weight: 450;

    /* 增加字间距以提高可读性 */
    letter-spacing: 0.01em;
  }
}

/* 高对比度模式的优化 */
@media (prefers-contrast: high) {
  .note-content,
  .markdown-preview {
    /* 高对比度模式下使用更粗的字体 */
    font-weight: 600;
    letter-spacing: 0.02em;

    /* 禁用字体平滑以获得最大对比度 */
    -webkit-font-smoothing: auto;
    -moz-osx-font-smoothing: auto;
    text-rendering: optimizeSpeed;
  }
}

/* 减少动画偏好的用户 */
@media (prefers-reduced-motion: reduce) {
  .note-content,
  .markdown-preview {
    /* 减少动画时保持高质量渲染 */
    text-rendering: optimizeLegibility;
    will-change: auto;
  }
}

/* ========================================
   打印和导出优化
   ======================================== */

/* 打印时的文本优化 */
@media print {
  * {
    /* 打印时使用最快的渲染模式 */
    -webkit-font-smoothing: auto;
    -moz-osx-font-smoothing: auto;
    text-rendering: optimizeSpeed;

    /* 打印时禁用字体特性 */
    font-feature-settings: normal;
    font-variant-ligatures: none;
  }

  .note-content,
  .markdown-preview {
    /* 打印时确保足够的对比度 */
    color: #000 !important;
    background: #fff !important;

    /* 打印时使用标准字体重量 */
    font-weight: 400;
    letter-spacing: 0;
  }
}

/* ========================================
   辅助功能和可访问性优化
   ======================================== */

/* 为视觉障碍用户优化 */
.high-contrast,
.accessibility-enhanced {
  /* 高对比度文本 */
  color: rgba(0, 0, 0, 0.95) !important;
  font-weight: 600 !important;
  letter-spacing: 0.02em !important;

  /* 禁用字体平滑以获得最大清晰度 */
  -webkit-font-smoothing: auto !important;
  -moz-osx-font-smoothing: auto !important;
  text-rendering: optimizeSpeed !important;
}

/* 大字体模式支持 */
@media (min-resolution: 1dppx) {
  .large-text .note-content,
  .large-text .markdown-preview {
    font-size: 1.2em;
    line-height: 1.7;
    letter-spacing: 0.01em;
  }
}

/* ========================================
   特殊情况和边缘案例处理
   ======================================== */

/* 非常小的便签的文本优化 */
.note[style*="width: 1"] .note-content,
.note[style*="width: 2"] .note-content {
  /* 小便签使用更粗的字体 */
  font-weight: 600;
  font-size: 12px;
  line-height: 1.4;
  letter-spacing: 0.02em;
}

/* 非常大的便签的文本优化 */
.note[style*="width: 8"] .note-content,
.note[style*="width: 9"] .note-content,
.note[style*="width: 10"] .note-content {
  /* 大便签可以使用更细的字体 */
  font-weight: 300;
  letter-spacing: -0.01em;
}

/* 只读模式的文本优化 */
.readonly .note-content,
.readonly .markdown-preview {
  /* 只读模式下优化可读性 */
  font-weight: 400;
  letter-spacing: 0.005em;
  text-rendering: optimizeLegibility;
}

/* 错误状态下的文本处理 */
.note.error .note-content {
  /* 错误状态下保持文本可读性 */
  text-rendering: optimizeLegibility;
  font-weight: 500;
}

/* ========================================
   缩放级别特定的文本优化
   ======================================== */

/* 极小缩放 (scale < 0.5) */
.scale-tiny .note-content,
.scale-tiny .markdown-preview {
  /* 极小缩放时使用最粗的字体确保可读性 */
  font-weight: 700 !important;
  letter-spacing: 0.03em !important;
  text-rendering: optimizeSpeed !important;

  /* 禁用字体特性以提高性能 */
  font-feature-settings: normal !important;
  font-variant-ligatures: none !important;

  /* 增加对比度 */
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.2) !important;
}

/* 小缩放 (0.5 <= scale < 0.8) */
.scale-small .note-content,
.scale-small .markdown-preview {
  /* 小缩放时使用较粗的字体 */
  font-weight: 600 !important;
  letter-spacing: 0.02em !important;
  text-rendering: optimizeSpeed !important;

  /* 简化字体特性 */
  font-feature-settings: "kern" 1 !important;
  font-variant-ligatures: none !important;
}

/* 大缩放 (1.5 < scale <= 2) */
.scale-large .note-content,
.scale-large .markdown-preview {
  /* 大缩放时可以使用更细的字体 */
  font-weight: 300 !important;
  letter-spacing: -0.01em !important;
  text-rendering: optimizeQuality !important;

  /* 启用更多字体特性 */
  font-feature-settings: "liga" 1, "kern" 1, "calt" 1, "swsh" 1 !important;
  font-variant-ligatures: contextual !important;
}

/* 超大缩放 (scale > 2) */
.scale-huge .note-content,
.scale-huge .markdown-preview {
  /* 超大缩放时使用最细的字体 */
  font-weight: 200 !important;
  letter-spacing: -0.02em !important;
  text-rendering: optimizeQuality !important;

  /* 启用所有字体特性 */
  font-feature-settings: "liga" 1, "kern" 1, "calt" 1, "swsh" 1, "ss01" 1 !important;
  font-variant-ligatures: contextual !important;
  font-optical-sizing: auto !important;
}

/* ========================================
   动态缩放过渡优化
   ======================================== */

/* 缩放过渡期间的优化 */
.note-container[style*="transition"] .note-content,
.note-container[style*="transition"] .markdown-preview {
  /* 过渡期间使用快速渲染 */
  text-rendering: optimizeSpeed !important;
  font-feature-settings: normal !important;

  /* 使用硬件加速 */
  will-change: transform, font-weight !important;
  transform: translateZ(0) !important;
}

/* 缩放过渡结束后恢复 */
.note-container:not([style*="transition"]) .note-content,
.note-container:not([style*="transition"]) .markdown-preview {
  /* 恢复高质量渲染 */
  will-change: auto !important;
  transform: none !important;
}
