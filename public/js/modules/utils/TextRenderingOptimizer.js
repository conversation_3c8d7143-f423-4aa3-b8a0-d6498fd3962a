/**
 * 文本渲染优化器 - TextRenderingOptimizer.js
 * 
 * 这个模块负责动态检测用户的设备、浏览器和显示环境，
 * 并应用最适合的文本渲染优化策略，确保在各种环境下
 * 都能获得最佳的文本清晰度和可读性。
 * 
 * 主要功能：
 * 1. 设备和浏览器检测
 * 2. DPI和分辨率检测
 * 3. 动态应用渲染优化
 * 4. Canvas缩放状态监听
 * 5. 用户偏好设置支持
 */

/**
 * 文本渲染优化器类
 */
class TextRenderingOptimizer {
  constructor() {
    this.deviceInfo = null;
    this.renderingStrategy = null;
    this.isInitialized = false;
    this.observers = [];
    
    // 绑定方法
    this.handleCanvasTransform = this.handleCanvasTransform.bind(this);
    this.handleResize = this.handleResize.bind(this);
  }

  /**
   * 初始化文本渲染优化器
   */
  async initialize() {
    if (this.isInitialized) return;

    try {
      // 检测设备信息
      this.deviceInfo = await this.detectDeviceInfo();
      
      // 确定渲染策略
      this.renderingStrategy = this.determineRenderingStrategy();
      
      // 应用初始优化
      this.applyOptimizations();
      
      // 设置事件监听器
      this.setupEventListeners();
      
      this.isInitialized = true;
      console.log('文本渲染优化器初始化完成', {
        deviceInfo: this.deviceInfo,
        strategy: this.renderingStrategy
      });
    } catch (error) {
      console.error('文本渲染优化器初始化失败:', error);
    }
  }

  /**
   * 检测设备信息
   * @returns {Object} 设备信息对象
   */
  async detectDeviceInfo() {
    const info = {
      // 基础信息
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      
      // 显示信息
      devicePixelRatio: window.devicePixelRatio || 1,
      screenWidth: screen.width,
      screenHeight: screen.height,
      viewportWidth: window.innerWidth,
      viewportHeight: window.innerHeight,
      
      // 浏览器检测
      browser: this.detectBrowser(),
      
      // 操作系统检测
      os: this.detectOS(),
      
      // 设备类型检测
      deviceType: this.detectDeviceType(),
      
      // DPI分类
      dpiCategory: this.categorizeDPI(),
      
      // 字体支持检测
      fontSupport: await this.detectFontSupport(),
      
      // 用户偏好
      preferences: this.detectUserPreferences()
    };

    return info;
  }

  /**
   * 检测浏览器类型
   * @returns {string} 浏览器名称
   */
  detectBrowser() {
    const ua = navigator.userAgent;
    
    if (ua.includes('Firefox')) return 'firefox';
    if (ua.includes('Safari') && !ua.includes('Chrome')) return 'safari';
    if (ua.includes('Chrome')) return 'chrome';
    if (ua.includes('Edge')) return 'edge';
    
    return 'unknown';
  }

  /**
   * 检测操作系统
   * @returns {string} 操作系统名称
   */
  detectOS() {
    const platform = navigator.platform.toLowerCase();
    const ua = navigator.userAgent.toLowerCase();
    
    if (platform.includes('mac') || ua.includes('mac')) return 'macos';
    if (platform.includes('win') || ua.includes('windows')) return 'windows';
    if (platform.includes('linux') || ua.includes('linux')) return 'linux';
    if (ua.includes('android')) return 'android';
    if (ua.includes('iphone') || ua.includes('ipad')) return 'ios';
    
    return 'unknown';
  }

  /**
   * 检测设备类型
   * @returns {string} 设备类型
   */
  detectDeviceType() {
    // 检测触摸支持
    const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    
    // 检测屏幕尺寸
    const screenSize = Math.max(screen.width, screen.height);
    
    if (!hasTouch && screenSize >= 1024) return 'desktop';
    if (hasTouch && screenSize >= 768) return 'tablet';
    if (hasTouch && screenSize < 768) return 'mobile';
    
    return 'desktop'; // 默认
  }

  /**
   * 分类DPI级别
   * @returns {string} DPI分类
   */
  categorizeDPI() {
    const dpr = window.devicePixelRatio || 1;
    
    if (dpr >= 3) return 'ultra-high'; // 4K/5K显示器
    if (dpr >= 2) return 'high';       // Retina显示器
    if (dpr >= 1.5) return 'medium';   // 中等DPI
    if (dpr >= 1) return 'standard';   // 标准DPI
    
    return 'low'; // 低DPI
  }

  /**
   * 检测字体支持
   * @returns {Object} 字体支持信息
   */
  async detectFontSupport() {
    const support = {
      variableFonts: false,
      opticalSizing: false,
      fontDisplay: false,
      fontFeatureSettings: false
    };

    try {
      // 检测可变字体支持
      if (CSS.supports('font-variation-settings', 'normal')) {
        support.variableFonts = true;
      }

      // 检测光学尺寸支持
      if (CSS.supports('font-optical-sizing', 'auto')) {
        support.opticalSizing = true;
      }

      // 检测font-display支持
      if (CSS.supports('font-display', 'swap')) {
        support.fontDisplay = true;
      }

      // 检测字体特性支持
      if (CSS.supports('font-feature-settings', '"liga" 1')) {
        support.fontFeatureSettings = true;
      }
    } catch (error) {
      console.warn('字体支持检测失败:', error);
    }

    return support;
  }

  /**
   * 检测用户偏好
   * @returns {Object} 用户偏好信息
   */
  detectUserPreferences() {
    return {
      colorScheme: window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light',
      contrast: window.matchMedia('(prefers-contrast: high)').matches ? 'high' : 'normal',
      reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
      fontSize: this.detectPreferredFontSize()
    };
  }

  /**
   * 检测用户偏好的字体大小
   * @returns {string} 字体大小偏好
   */
  detectPreferredFontSize() {
    // 创建测试元素检测默认字体大小
    const testElement = document.createElement('div');
    testElement.style.fontSize = '1rem';
    testElement.style.position = 'absolute';
    testElement.style.visibility = 'hidden';
    document.body.appendChild(testElement);
    
    const computedSize = window.getComputedStyle(testElement).fontSize;
    const size = parseFloat(computedSize);
    
    document.body.removeChild(testElement);
    
    if (size >= 18) return 'large';
    if (size >= 16) return 'normal';
    return 'small';
  }

  /**
   * 确定渲染策略
   * @returns {Object} 渲染策略配置
   */
  determineRenderingStrategy() {
    const { deviceInfo } = this;
    
    const strategy = {
      fontSmoothing: 'auto',
      textRendering: 'optimizeLegibility',
      fontWeight: 400,
      letterSpacing: '0',
      fontFeatures: true,
      hardwareAcceleration: false
    };

    // 根据DPI调整策略
    switch (deviceInfo.dpiCategory) {
      case 'ultra-high':
        strategy.fontSmoothing = 'antialiased';
        strategy.textRendering = 'optimizeQuality';
        strategy.fontWeight = 300;
        strategy.letterSpacing = '-0.01em';
        break;
        
      case 'high':
        strategy.fontSmoothing = 'antialiased';
        strategy.textRendering = 'optimizeQuality';
        strategy.fontWeight = 400;
        break;
        
      case 'standard':
        strategy.fontSmoothing = 'subpixel-antialiased';
        strategy.textRendering = 'optimizeLegibility';
        strategy.fontWeight = 400;
        strategy.letterSpacing = '0.01em';
        break;
        
      case 'low':
        strategy.fontSmoothing = 'auto';
        strategy.textRendering = 'optimizeSpeed';
        strategy.fontWeight = 500;
        strategy.letterSpacing = '0.02em';
        strategy.fontFeatures = false;
        break;
    }

    // 根据操作系统调整
    if (deviceInfo.os === 'windows' && deviceInfo.dpiCategory === 'standard') {
      strategy.fontWeight += 100; // Windows下字体通常需要更粗
    }

    // 根据浏览器调整
    if (deviceInfo.browser === 'firefox') {
      strategy.fontSmoothing = 'auto'; // Firefox有自己的渲染逻辑
    }

    // 根据设备类型调整
    if (deviceInfo.deviceType === 'mobile') {
      strategy.fontWeight += 100; // 移动设备需要更粗的字体
      strategy.letterSpacing = '0.01em';
    }

    return strategy;
  }

  /**
   * 应用优化设置
   */
  applyOptimizations() {
    const { renderingStrategy, deviceInfo } = this;
    
    // 创建动态样式
    const styleId = 'text-rendering-dynamic-optimization';
    let styleElement = document.getElementById(styleId);
    
    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = styleId;
      document.head.appendChild(styleElement);
    }

    // 生成CSS规则
    const css = this.generateOptimizationCSS(renderingStrategy, deviceInfo);
    styleElement.textContent = css;
    
    // 添加设备信息到body类名
    this.addDeviceClasses();
  }

  /**
   * 生成优化CSS
   * @param {Object} strategy 渲染策略
   * @param {Object} deviceInfo 设备信息
   * @returns {string} CSS字符串
   */
  generateOptimizationCSS(strategy, deviceInfo) {
    return `
      /* 动态文本渲染优化 */
      .note-content,
      .markdown-preview {
        -webkit-font-smoothing: ${strategy.fontSmoothing} !important;
        -moz-osx-font-smoothing: ${strategy.fontSmoothing === 'subpixel-antialiased' ? 'auto' : 'grayscale'} !important;
        text-rendering: ${strategy.textRendering} !important;
        font-weight: ${strategy.fontWeight} !important;
        letter-spacing: ${strategy.letterSpacing} !important;
        ${strategy.fontFeatures ? 'font-feature-settings: "liga" 1, "kern" 1, "calt" 1 !important;' : 'font-feature-settings: normal !important;'}
        ${strategy.hardwareAcceleration ? 'will-change: transform; transform: translateZ(0);' : ''}
      }
      
      /* 设备特定优化 */
      .device-${deviceInfo.os}.dpi-${deviceInfo.dpiCategory} .note-content,
      .device-${deviceInfo.os}.dpi-${deviceInfo.dpiCategory} .markdown-preview {
        font-family: var(--font-family-system) !important;
      }
    `;
  }

  /**
   * 添加设备相关的CSS类
   */
  addDeviceClasses() {
    const { deviceInfo } = this;
    const body = document.body;
    
    // 清除旧的设备类
    body.className = body.className.replace(/\b(device-|dpi-|browser-|type-)\w+/g, '');
    
    // 添加新的设备类
    body.classList.add(
      `device-${deviceInfo.os}`,
      `dpi-${deviceInfo.dpiCategory}`,
      `browser-${deviceInfo.browser}`,
      `type-${deviceInfo.deviceType}`
    );
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 监听Canvas变换事件
    document.addEventListener('canvas-transform-updated', this.handleCanvasTransform);
    
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize);
    
    // 监听用户偏好变化
    this.setupPreferenceListeners();
  }

  /**
   * 处理Canvas变换事件
   * @param {CustomEvent} event Canvas变换事件
   */
  handleCanvasTransform(event) {
    const { scale } = event.detail;
    
    // 根据缩放级别调整文本渲染
    this.adjustForScale(scale);
  }

  /**
   * 根据缩放级别调整文本渲染
   * @param {number} scale 缩放比例
   */
  adjustForScale(scale) {
    const noteContainer = document.getElementById('note-container');
    if (!noteContainer) return;

    // 移除旧的缩放类
    noteContainer.className = noteContainer.className.replace(/\bscale-\w+/g, '');
    
    // 添加新的缩放类
    if (scale < 0.5) {
      noteContainer.classList.add('scale-tiny');
    } else if (scale < 0.8) {
      noteContainer.classList.add('scale-small');
    } else if (scale > 1.5) {
      noteContainer.classList.add('scale-large');
    } else if (scale > 2) {
      noteContainer.classList.add('scale-huge');
    }
  }

  /**
   * 处理窗口大小变化
   */
  handleResize() {
    // 防抖处理
    clearTimeout(this.resizeTimeout);
    this.resizeTimeout = setTimeout(() => {
      this.updateDeviceInfo();
    }, 250);
  }

  /**
   * 更新设备信息
   */
  async updateDeviceInfo() {
    const newDeviceInfo = await this.detectDeviceInfo();
    
    // 检查是否有重要变化
    if (this.hasSignificantChange(this.deviceInfo, newDeviceInfo)) {
      this.deviceInfo = newDeviceInfo;
      this.renderingStrategy = this.determineRenderingStrategy();
      this.applyOptimizations();
    }
  }

  /**
   * 检查设备信息是否有重要变化
   * @param {Object} oldInfo 旧设备信息
   * @param {Object} newInfo 新设备信息
   * @returns {boolean} 是否有重要变化
   */
  hasSignificantChange(oldInfo, newInfo) {
    if (!oldInfo) return true;
    
    return (
      oldInfo.devicePixelRatio !== newInfo.devicePixelRatio ||
      oldInfo.dpiCategory !== newInfo.dpiCategory ||
      oldInfo.deviceType !== newInfo.deviceType ||
      Math.abs(oldInfo.viewportWidth - newInfo.viewportWidth) > 100
    );
  }

  /**
   * 设置用户偏好监听器
   */
  setupPreferenceListeners() {
    // 监听颜色主题变化
    const colorSchemeQuery = window.matchMedia('(prefers-color-scheme: dark)');
    colorSchemeQuery.addListener(() => {
      this.updateDeviceInfo();
    });

    // 监听对比度偏好变化
    const contrastQuery = window.matchMedia('(prefers-contrast: high)');
    contrastQuery.addListener(() => {
      this.updateDeviceInfo();
    });
  }

  /**
   * 销毁优化器
   */
  destroy() {
    // 移除事件监听器
    document.removeEventListener('canvas-transform-updated', this.handleCanvasTransform);
    window.removeEventListener('resize', this.handleResize);
    
    // 清除定时器
    clearTimeout(this.resizeTimeout);
    
    // 移除动态样式
    const styleElement = document.getElementById('text-rendering-dynamic-optimization');
    if (styleElement) {
      styleElement.remove();
    }
    
    this.isInitialized = false;
  }
}

// 创建全局实例
const textRenderingOptimizer = new TextRenderingOptimizer();

// 导出
export { TextRenderingOptimizer, textRenderingOptimizer };
export default textRenderingOptimizer;
