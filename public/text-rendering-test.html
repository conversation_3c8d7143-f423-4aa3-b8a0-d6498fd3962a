<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文本渲染优化测试</title>
    <link rel="stylesheet" href="css/text-rendering-optimization.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            font-family: var(--font-family-system, -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif);
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        
        .device-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-family: var(--font-family-mono, monospace);
            font-size: 12px;
        }
        
        .note-sample {
            width: 300px;
            height: 200px;
            background: #fff3cd;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin: 10px;
            display: inline-block;
            vertical-align: top;
        }
        
        .note-content {
            width: 100%;
            height: 100%;
            padding: 12px 14px;
            border: none;
            outline: none;
            resize: none;
            background: transparent;
            font-size: 14px;
            line-height: 1.6;
            color: rgba(0, 0, 0, 0.87);
            border-radius: 8px;
        }
        
        .markdown-preview {
            width: 100%;
            height: 100%;
            padding: 12px 14px;
            font-size: 14px;
            line-height: 1.6;
            color: rgba(0, 0, 0, 0.87);
            background: transparent;
            border-radius: 8px;
        }
        
        .scale-controls {
            margin: 20px 0;
        }
        
        .scale-button {
            margin: 5px;
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .scale-button:hover {
            background: #f0f0f0;
        }
        
        .scale-button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .samples-container {
            transform-origin: top left;
            transition: transform 0.3s ease;
        }
        
        .info-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            font-size: 12px;
            z-index: 1000;
        }
        
        .optimization-status {
            margin-top: 10px;
            padding: 10px;
            background: #e8f5e8;
            border-radius: 4px;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>文本渲染优化测试页面</h1>
        <p>这个页面用于测试新的文本渲染优化系统在不同设备和缩放级别下的效果。</p>
        
        <div class="test-section">
            <div class="test-title">设备信息</div>
            <div class="device-info" id="device-info">
                正在检测设备信息...
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">缩放测试</div>
            <div class="scale-controls">
                <button class="scale-button active" data-scale="1">100%</button>
                <button class="scale-button" data-scale="0.5">50%</button>
                <button class="scale-button" data-scale="0.8">80%</button>
                <button class="scale-button" data-scale="1.5">150%</button>
                <button class="scale-button" data-scale="2">200%</button>
            </div>
            
            <div class="samples-container" id="samples-container">
                <div class="note-sample">
                    <textarea class="note-content" placeholder="这是一个便签文本区域。请输入一些文字来测试文本渲染效果。支持中文、English、数字123和特殊符号！@#$%">这是一个测试便签。

包含中文文字、English text、数字123456、特殊符号！@#$%^&*()

测试不同字体重量和渲染效果。</textarea>
                </div>
                
                <div class="note-sample">
                    <div class="markdown-preview">
                        <h3>Markdown 预览测试</h3>
                        <p>这是一个 <strong>粗体文字</strong> 和 <em>斜体文字</em> 的测试。</p>
                        <ul>
                            <li>列表项目 1</li>
                            <li>列表项目 2</li>
                            <li>包含 <code>代码</code> 的项目</li>
                        </ul>
                        <blockquote>
                            这是一个引用块，用于测试不同的文本样式。
                        </blockquote>
                    </div>
                </div>
                
                <div class="note-sample">
                    <div class="markdown-preview">
                        <h4>代码块测试</h4>
                        <pre><code>function testFunction() {
  console.log("Hello World");
  return true;
}</code></pre>
                        <p>行内代码：<code>const x = 42;</code></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="info-panel" id="info-panel">
        <h4>实时信息</h4>
        <div id="current-scale">当前缩放: 100%</div>
        <div id="applied-classes">应用的类: 无</div>
        <div id="font-weight">字体重量: 检测中...</div>
        <div id="text-rendering">文本渲染: 检测中...</div>
        <div class="optimization-status" id="optimization-status">
            文本渲染优化器状态: 初始化中...
        </div>
    </div>

    <script type="module">
        import { textRenderingOptimizer } from './js/modules/utils/TextRenderingOptimizer.js';
        
        // 初始化文本渲染优化器
        async function initOptimizer() {
            try {
                await textRenderingOptimizer.initialize();
                document.getElementById('optimization-status').textContent = '文本渲染优化器状态: 已激活';
                updateDeviceInfo();
            } catch (error) {
                console.error('优化器初始化失败:', error);
                document.getElementById('optimization-status').textContent = '文本渲染优化器状态: 初始化失败';
            }
        }
        
        // 更新设备信息显示
        function updateDeviceInfo() {
            const deviceInfo = textRenderingOptimizer.deviceInfo;
            if (deviceInfo) {
                document.getElementById('device-info').innerHTML = `
                    浏览器: ${deviceInfo.browser}<br>
                    操作系统: ${deviceInfo.os}<br>
                    设备类型: ${deviceInfo.deviceType}<br>
                    DPI分类: ${deviceInfo.dpiCategory}<br>
                    设备像素比: ${deviceInfo.devicePixelRatio}<br>
                    屏幕尺寸: ${deviceInfo.screenWidth} x ${deviceInfo.screenHeight}<br>
                    视口尺寸: ${deviceInfo.viewportWidth} x ${deviceInfo.viewportHeight}<br>
                    颜色主题: ${deviceInfo.preferences.colorScheme}<br>
                    对比度: ${deviceInfo.preferences.contrast}
                `;
            }
        }
        
        // 缩放控制
        function setupScaleControls() {
            const scaleButtons = document.querySelectorAll('.scale-button');
            const samplesContainer = document.getElementById('samples-container');
            const currentScaleDisplay = document.getElementById('current-scale');
            const appliedClassesDisplay = document.getElementById('applied-classes');
            
            scaleButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // 更新按钮状态
                    scaleButtons.forEach(b => b.classList.remove('active'));
                    button.classList.add('active');
                    
                    // 应用缩放
                    const scale = parseFloat(button.dataset.scale);
                    samplesContainer.style.transform = `scale(${scale})`;
                    
                    // 更新显示信息
                    currentScaleDisplay.textContent = `当前缩放: ${Math.round(scale * 100)}%`;
                    
                    // 模拟缩放类的应用
                    samplesContainer.className = 'samples-container';
                    if (scale < 0.5) {
                        samplesContainer.classList.add('scale-tiny');
                        appliedClassesDisplay.textContent = '应用的类: scale-tiny';
                    } else if (scale < 0.8) {
                        samplesContainer.classList.add('scale-small');
                        appliedClassesDisplay.textContent = '应用的类: scale-small';
                    } else if (scale > 2) {
                        samplesContainer.classList.add('scale-huge');
                        appliedClassesDisplay.textContent = '应用的类: scale-huge';
                    } else if (scale > 1.5) {
                        samplesContainer.classList.add('scale-large');
                        appliedClassesDisplay.textContent = '应用的类: scale-large';
                    } else {
                        appliedClassesDisplay.textContent = '应用的类: 无';
                    }
                    
                    // 更新字体信息
                    updateFontInfo();
                });
            });
        }
        
        // 更新字体信息
        function updateFontInfo() {
            const noteContent = document.querySelector('.note-content');
            if (noteContent) {
                const computedStyle = window.getComputedStyle(noteContent);
                document.getElementById('font-weight').textContent = 
                    `字体重量: ${computedStyle.fontWeight}`;
                document.getElementById('text-rendering').textContent = 
                    `文本渲染: ${computedStyle.textRendering}`;
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async () => {
            await initOptimizer();
            setupScaleControls();
            updateFontInfo();
            
            // 定期更新字体信息
            setInterval(updateFontInfo, 1000);
        });
    </script>
</body>
</html>
